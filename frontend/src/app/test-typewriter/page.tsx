'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Typewriter } from '@/components/ui/typewriter-text';

export default function TestTypewriterPage() {
  const [isConnecting, setIsConnecting] = useState(false);

  const handleConnect = () => {
    setIsConnecting(true);
    // Simulate connection process
    setTimeout(() => {
      setIsConnecting(false);
    }, 10000); // 10 seconds to see the full cycle
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-background">
      <div className="max-w-md w-full space-y-8 p-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Typewriter Loading Test</h1>
          <p className="text-muted-foreground mb-8">
            Click the button to see the cool typewriter loading states in action!
          </p>
        </div>

        <div className="space-y-4">
          <Button
            onClick={handleConnect}
            disabled={isConnecting}
            className="w-full min-w-[280px] h-12"
          >
            {isConnecting ? (
              <Typewriter
                text={[
                  "articulating your custom server...",
                  "testing connection...",
                  "generating redirect url..."
                ]}
                speed={50}
                deleteSpeed={30}
                delay={800}
                loop={true}
                className="text-sm"
              />
            ) : (
              "Connect MCP Server"
            )}
          </Button>

          <div className="text-center">
            <Button
              variant="outline"
              onClick={() => setIsConnecting(!isConnecting)}
              className="text-sm"
            >
              {isConnecting ? "Stop Loading" : "Start Loading"}
            </Button>
          </div>
        </div>

        <div className="text-center text-sm text-muted-foreground">
          <p>The typewriter effect cycles through three states:</p>
          <ul className="mt-2 space-y-1">
            <li>1. "articulating your custom server..."</li>
            <li>2. "testing connection..."</li>
            <li>3. "generating redirect url..."</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
